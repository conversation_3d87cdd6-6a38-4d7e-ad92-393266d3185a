[package]
name = "actix-example-api"
version = "0.1.0"
authors = ["<PERSON> <<EMAIL>>"]
edition = "2021"
publish = false

[dependencies]
actix-example-service = { path = "../service" }
actix-files = "0.6"
actix-http = "3"
actix-rt = "2.8"
actix-service = "2"
actix-web = "4"
tera = "1.19.0"
dotenvy = "0.15"
listenfd = "1"
serde = "1"
tracing-subscriber = { version = "0.3.17", features = ["env-filter"] }
entity = { path = "../entity" }
migration = { path = "../migration" }
