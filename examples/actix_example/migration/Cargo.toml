[package]
name = "migration"
version = "0.1.0"
edition = "2021"
publish = false

[lib]
name = "migration"
path = "src/lib.rs"

[dependencies]
async-std = { version = "1", features = ["attributes", "tokio1"] }

[dependencies.sea-orm-migration]
path = "../../../sea-orm-migration" # remove this line in your own project
version = "~1.1.14" # sea-orm-migration version
features = [
  # Enable following runtime and db backend features if you want to run migration via CLI
  # "runtime-actix-native-tls",
  # "sqlx-mysql",
]
