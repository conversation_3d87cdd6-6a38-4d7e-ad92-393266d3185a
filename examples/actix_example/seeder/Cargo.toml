[package]
name = "seeder"
version = "0.1.0"
edition = "2021"
publish = false

[lib]
name = "seeder"
path = "src/lib.rs"

[dependencies]
async-std = { version = "1", features = ["attributes", "tokio1"] }
entity = { path = "../entity" }
migration = { path = "../migration" }

[dependencies.sea-orm-migration]
path = "../../../sea-orm-migration" # remove this line in your own project
version = "~1.1.13"
features = [
  # Enable at least one `ASYNC_RUNTIME` and `DATABASE_DRIVER` feature if you want to run migration via CLI.
  # View the list of supported features at https://www.sea-ql.org/SeaORM/docs/install-and-config/database-and-async-runtime.
  # e.g.
  # "runtime-tokio-rustls",  # `ASYNC_RUNTIME` feature
  # "sqlx-mysql",         # `DATABASE_DRIVER` feature
]
