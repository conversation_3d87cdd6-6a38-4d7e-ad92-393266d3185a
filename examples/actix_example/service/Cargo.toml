[package]
name = "actix-example-service"
version = "0.1.0"
edition = "2021"

# See more keys and their definitions at https://doc.rust-lang.org/cargo/reference/manifest.html

[dependencies]
entity = { path = "../entity" }

[dependencies.sea-orm]
path = "../../../" # remove this line in your own project
version = "~1.1.14" # sea-orm version
features = [
    "debug-print",
    "runtime-async-std-native-tls",
    "sqlx-mysql",
    # "sqlx-postgres",
    # "sqlx-sqlite",
]

[dev-dependencies]
tokio = { version = "1.20.0", features = ["macros", "rt"] }

[features]
mock = ["sea-orm/mock"]

[[test]]
name = "mock"
required-features = ["mock"]
