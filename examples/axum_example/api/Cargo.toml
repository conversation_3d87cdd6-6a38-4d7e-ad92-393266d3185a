[package]
name = "axum-example-api"
version = "0.1.0"
authors = ["<PERSON><PERSON><PERSON> <huang<PERSON><PERSON>@126.com>"]
edition = "2021"
publish = false

[dependencies]
axum-example-service = { path = "../service" }
tokio = { version = "1.34.0", features = ["full"] }
axum = "0.8"
tower = "0.5"
tower-http = { version = "0.6", features = ["fs"] }
tower-cookies = "0.11"
anyhow = "1.0.75"
dotenvy = "0.15.7"
serde = "1.0.193"
serde_json = "1.0.108"
tera = "1.19.1"
tracing-subscriber = { version = "0.3.18", features = ["env-filter"] }
entity = { path = "../entity" }
migration = { path = "../migration" }
