.field-error {
  border: 1px solid #ff0000 !important;
}

.field-error-flash {
  color: #ff0000;
  display: block;
  margin: -10px 0 10px 0;
}

.field-success {
  border: 1px solid #5ab953 !important;
}

.field-success-flash {
  color: #5ab953;
  display: block;
  margin: -10px 0 10px 0;
}

span.completed {
  text-decoration: line-through;
}

form.inline {
  display: inline;
}

form.link,
button.link {
  display: inline;
  color: #1eaedb;
  border: none;
  outline: none;
  background: none;
  cursor: pointer;
  padding: 0;
  margin: 0 0 0 0;
  height: inherit;
  text-decoration: underline;
  font-size: inherit;
  text-transform: none;
  font-weight: normal;
  line-height: inherit;
  letter-spacing: inherit;
}

form.link:hover,
button.link:hover {
  color: #0fa0ce;
}

button.small {
  height: 20px;
  padding: 0 10px;
  font-size: 10px;
  line-height: 20px;
  margin: 0 2.5px;
}

.post:hover {
  background-color: #bce2ee;
}

.post td {
  padding: 5px;
  width: 150px;
}

#delete-button {
  color: red;
  border-color: red;
}
