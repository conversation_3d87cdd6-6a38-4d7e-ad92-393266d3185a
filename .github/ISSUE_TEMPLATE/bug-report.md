---
name: Bug Report
about: Report a bug or design flaw
title: ""
labels: ""
assignees: ""
type: Bug
---

<!--

Welcome! Thank you for reporting bugs!

First of all, please star our repo. Your support is vital to the continued maintenance of SeaORM.

Want to ask a question? You can reach us via:

- Discord: https://discord.com/invite/uCPdDXzbdv
- GitHub Discussions: https://github.com/SeaQL/sea-orm/discussions/new

Please make sure that you are not asking for a missing feature; a bug is incorrect behavior -
either in the feature specification or implementation. Feature requests should be first raised in discussions.

Please also make sure your description is clear and precise - maintainers don't have access to your
code and can't see what you have seen. Please avoid vague descriptions like "they are different"
or "the program crashes" - in either case, provide exact information.

If you are certain there is a bug, please provide a reproducible example, which helps the investigator
to pin-point the bug and the implementor to verify that a solution is satisfactory. Bug reports without
reproducible example may be closed immediately or dangle forever.

Finally, please search for existing issues and discussions before submission. Feel free to revive old
threads if you have new information to add, but please don't ask for ETA or "+1".

-->

## Description

<!-- Briefly describe the bug -->

## Steps to Reproduce

1. <!-- First step -->
2. <!-- Then -->
3. <!-- And so on -->

### Expected Behavior

<!-- What is expected to happen? -->

### Actual Behavior

<!-- What actually happened? -->

### Reproduces How Often

<!-- Is it always reproducible? -->

### Workarounds

<!-- What experiments have you done to understand / workaround the bug? -->

## Reproducible Example

<!-- Please add a minimal reproducible example under https://github.com/SeaQL/sea-orm/tree/master/issues, and open a PR subsequently. -->

## Versions

<!-- You can get this information from the output of `cargo tree | grep sea-` from the console. Also, please include the database and OS that you are running. -->
